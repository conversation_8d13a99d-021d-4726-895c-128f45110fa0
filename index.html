<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法弈 - 智能法律诉讼辅助系统</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4f8 50%, #f0f8f5 100%);
            min-height: 100vh;
            color: #333333;
            overflow-x: hidden;
        }

        /* 添加背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(179, 205, 224, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(162, 217, 206, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(245, 166, 35, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* 页面切换 */
        .page {
            display: none;
            min-height: 100vh;
            animation: fadeIn 0.8s ease;
        }

        .page.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 欢迎页样式 */
        .welcome-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 2rem;
            text-align: center;
        }

        .logo-text {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }

        .tagline {
            font-size: 1.2rem;
            color: #666;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 4rem;
        }

        /* 功能网格 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            max-width: 800px;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px rgba(179, 205, 224, 0.3);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1.5rem;
            color: #B3CDE0;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            color: #F5A623;
            transform: scale(1.1) rotate(5deg);
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.6;
        }

        /* 按钮样式 */
        .start-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 3rem;
            font-size: 1.2rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .start-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 体验页样式 */
        .experience-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 2rem;
            min-height: 100vh;
        }

        .section-title {
            text-align: center;
            font-size: 2rem;
            font-weight: 500;
            color: #333;
            margin-bottom: 3rem;
        }

        .input-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .input-group {
            position: relative;
        }

        .input-group label {
            display: block;
            font-weight: 500;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .input-group textarea {
            width: 100%;
            min-height: 200px;
            padding: 1.5rem;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-family: inherit;
            font-size: 1.1rem;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #B3CDE0;
            box-shadow: 0 0 0 3px rgba(179, 205, 224, 0.1);
        }

        .example-btn {
            display: none;
        }

        .analyze-section {
            text-align: center;
        }

        .analyze-btn {
            background: linear-gradient(135deg, #B3CDE0 0%, #A2D9CE 100%);
            color: white;
            border: none;
            padding: 1rem 2.5rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(179, 205, 224, 0.3);
        }

        .analyze-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(179, 205, 224, 0.4);
        }

        /* 分析过程展示区 */
        .analysis-section {
            margin: 4rem 0;
            border-radius: 20px;
            overflow: hidden;
        }

        .analysis-background {
            position: relative;
            width: 100%;
            height: 400px;
            background: linear-gradient(135deg, rgba(179, 205, 224, 0.1) 0%, rgba(162, 217, 206, 0.1) 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #networkCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0.6;
        }

        .analysis-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .analysis-text h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 500;
        }

        .progress-dots {
            display: inline-flex;
            gap: 0.5rem;
        }

        .progress-dots span {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #B3CDE0;
            animation: pulse 1.5s infinite;
        }

        .progress-dots span:nth-child(2) {
            animation-delay: 0.3s;
        }

        .progress-dots span:nth-child(3) {
            animation-delay: 0.6s;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .view-report-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 2rem;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
        }

        .view-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 结果展示区 */
        .results-section {
            margin-top: 4rem;
        }

        .evidence-chain {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-chain h3 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 500;
        }

        .evidence-timeline {
            position: relative;
            padding: 2rem 0;
            min-height: 800px;
        }

        .evidence-container {
            display: grid;
            grid-template-columns: 280px 1fr 280px;
            gap: 1.5rem;
            height: 800px;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .evidence-list {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-list h4 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .evidence-list-item {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #B3CDE0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .evidence-list-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(179, 205, 224, 0.3);
        }

        .evidence-list-item.evidence_owned {
            border-left-color: #A2D9CE;
        }

        .evidence-list-item.evidence_owned::before {
            content: '■';
            color: #A2D9CE;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.evidence_gap {
            border-left-color: #ff6b6b;
        }

        .evidence-list-item.evidence_gap::before {
            content: '●';
            color: #ff6b6b;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.conclusion {
            border-left-color: #F5A623;
        }

        .evidence-list-item.conclusion::before {
            content: '♦';
            color: #F5A623;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item.final_claim {
            border-left-color: #9b59b6;
        }

        .evidence-list-item.final_claim::before {
            content: '▲';
            color: #9b59b6;
            font-size: 1.2rem;
            margin-right: 0.5rem;
            float: left;
        }

        .evidence-list-item h5 {
            color: #333;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .evidence-list-item p {
            color: #666;
            font-size: 0.8rem;
            line-height: 1.4;
            margin: 0;
        }

        .evidence-graph {
            position: relative;
            width: 100%;
            height: 800px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 16px;
            overflow: visible;
            padding: 20px;
            box-sizing: border-box;
        }

        .evidence-report {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 1.5rem;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .evidence-report h4 {
            color: #333;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .report-section {
            margin-bottom: 1.5rem;
        }

        .report-section h5 {
            color: #B3CDE0;
            font-size: 1rem;
            margin-bottom: 0.8rem;
            font-weight: 500;
        }

        .report-section p {
            color: #666;
            font-size: 0.9rem;
            line-height: 1.6;
            margin-bottom: 0.8rem;
        }

        .report-highlight {
            background: rgba(245, 166, 35, 0.1);
            border-left: 3px solid #F5A623;
            padding: 0.8rem;
            border-radius: 6px;
            margin: 0.5rem 0;
        }

        .evidence-node {
            position: absolute;
            background: white;
            border-radius: 16px;
            padding: 1rem;
            width: 140px;
            height: 90px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 3px solid #B3CDE0;
            transition: all 0.3s ease;
            opacity: 0;
            transform: scale(0.8);
            animation: nodeAppear 0.8s ease forwards;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .evidence-node:hover {
            z-index: 10;
        }

        .evidence-node.evidence_owned {
            border-color: #A2D9CE;
            background: rgba(162, 217, 206, 0.1);
            border-radius: 8px; /* 圆角矩形 - 已有证据 */
            border-width: 4px;
            border-style: solid;
        }

        .evidence-node.evidence_owned:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(162, 217, 206, 0.4);
        }

        .evidence-node.evidence_gap {
            border-color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 50%; /* 圆形 - 证据缺口 */
            border-style: dashed;
            border-width: 4px;
        }

        .evidence-node.evidence_gap:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .evidence-node.conclusion {
            border-color: #F5A623;
            background: rgba(245, 166, 35, 0.1);
            border-radius: 50%; /* 圆形 - 结论节点 */
            border-width: 4px;
            border-style: solid;
        }

        .evidence-node.conclusion:hover {
            transform: scale(1.05); /* 移除旋转，只保持放大 */
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.4);
        }

        .evidence-node.final_claim {
            border-color: #9b59b6;
            background: rgba(155, 89, 182, 0.1);
            border-radius: 50%; /* 圆形 - 最终诉求 */
            border-width: 4px;
            border-style: solid;
            position: relative;
        }

        .evidence-node.final_claim:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(155, 89, 182, 0.4);
        }

        .evidence-node.evidence_gap::before {
            content: '⚠️';
            position: absolute;
            top: 0.3rem;
            right: 0.3rem;
            font-size: 0.9rem;
        }

        @keyframes nodeAppear {
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .evidence-node h4 {
            color: #333;
            margin: 0;
            font-weight: 500;
            font-size: 0.85rem;
            line-height: 1.2;
            text-align: center;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        /* 确保所有节点类型的文字样式统一 */
        .evidence-node.evidence_owned h4,
        .evidence-node.evidence_gap h4,
        .evidence-node.conclusion h4,
        .evidence-node.final_claim h4 {
            color: #333;
            margin: 0;
            font-weight: 500;
            font-size: 0.85rem;
            line-height: 1.2;
            text-align: center;
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }

        .evidence-connection {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .evidence-connection svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .evidence-connection line {
            stroke: #B3CDE0;
            stroke-width: 3;
            opacity: 0;
            animation: lineAppear 1s ease forwards;
            marker-end: url(#arrowhead);
        }

        .evidence-connection.gap line {
            stroke: #ff6b6b;
            stroke-width: 3;
            stroke-dasharray: 8,4;
            marker-end: url(#arrowhead-gap);
        }

        @keyframes lineAppear {
            to {
                opacity: 0.8;
            }
        }

        /* 箭头标记 */
        .arrow-marker {
            fill: #B3CDE0;
        }

        .arrow-marker-gap {
            fill: #ff6b6b;
        }

        .full-report-btn {
            display: block;
            margin: 2rem auto 0;
            background: linear-gradient(135deg, #A2D9CE 0%, #B3CDE0 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(162, 217, 206, 0.3);
        }

        .full-report-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(162, 217, 206, 0.4);
        }

        /* 综合报告 */
        .full-report {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .report-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 2rem;
            gap: 1rem;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 1rem 1.5rem;
            font-size: 1rem;
            font-weight: 500;
            color: #666;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-btn.active {
            color: #B3CDE0;
            background: rgba(179, 205, 224, 0.1);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #B3CDE0;
        }

        .tab-panel {
            display: none;
            animation: fadeIn 0.5s ease;
        }

        .tab-panel.active {
            display: block;
        }

        .report-content {
            line-height: 1.8;
            color: #333;
        }

        .report-content h4 {
            color: #B3CDE0;
            font-size: 1.3rem;
            margin: 2rem 0 1rem 0;
            font-weight: 500;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 0.5rem;
        }

        .statute-item, .case-item {
            background: rgba(179, 205, 224, 0.05);
            border-radius: 12px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #B3CDE0;
        }

        .statute-item h5, .case-item h5 {
            color: #333;
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .document-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            font-family: 'Noto Sans SC', serif;
        }

        .document-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 1rem;
        }

        .document-header h3 {
            font-size: 2rem;
            color: #333;
            font-weight: 700;
        }

        .download-btn {
            background: linear-gradient(135deg, #F5A623 0%, #ff8c42 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 500;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(245, 166, 35, 0.3);
            margin-top: 2rem;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(245, 166, 35, 0.4);
        }

        /* 调试面板 */
        .debug-panel {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
        }

        .debug-panel button {
            margin-top: 5px;
            padding: 5px 10px;
            background: #333;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .logo-text {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <!-- 欢迎首页 -->
    <div id="welcomePage" class="page active">
        <div class="welcome-container">
            <!-- 顶部Logo和标语 -->
            <header class="welcome-header">
                <div class="logo">
                    <h1 class="logo-text">法弈</h1>
                    <p class="tagline">洞见法律脉络，预见诉讼未来</p>
                </div>
            </header>

            <!-- 中央功能简介区 -->
            <main class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="4" fill="currentColor"/>
                            <circle cx="16" cy="16" r="3" fill="currentColor"/>
                            <circle cx="48" cy="16" r="3" fill="currentColor"/>
                            <circle cx="16" cy="48" r="3" fill="currentColor"/>
                            <circle cx="48" cy="48" r="3" fill="currentColor"/>
                            <path d="M32 28L19 19M32 28L45 19M32 36L19 45M32 36L45 45" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">案件智能解构</h3>
                    <p class="feature-desc">深入剖析案情，提炼关键要素</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M32 8L40 16H24L32 8Z" fill="currentColor"/>
                            <rect x="20" y="16" width="24" height="32" rx="2" fill="currentColor" opacity="0.7"/>
                            <circle cx="16" cy="32" r="4" fill="currentColor"/>
                            <circle cx="48" cy="32" r="4" fill="currentColor"/>
                            <path d="M20 32H16M44 32H48" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">证据链诊断</h3>
                    <p class="feature-desc">梳理现有证据，预警潜在缺口</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="16" y="12" width="32" height="40" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M24 20H40M24 28H40M24 36H36" stroke="white" stroke-width="2"/>
                            <path d="M32 8L36 12H28L32 8Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">法律与案例指引</h3>
                    <p class="feature-desc">智能匹配法规，精准推荐相似判例</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="20" y="8" width="24" height="48" rx="2" fill="currentColor" opacity="0.7"/>
                            <path d="M28 16H36M28 24H36M28 32H36M28 40H32" stroke="white" stroke-width="2"/>
                            <path d="M16 20L20 16V24L16 20Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">诉讼文书生成</h3>
                    <p class="feature-desc">一键生成专业、规范的法律文书初稿</p>
                </div>
            </main>

            <!-- 底部体验按钮 -->
            <footer class="welcome-footer">
                <button id="startExperienceBtn" class="start-btn">
                    <span>立即体验</span>
                </button>
            </footer>
        </div>
    </div>

    <!-- 核心体验页 -->
    <div id="experiencePage" class="page">
        <div class="experience-container">
            <!-- 信息输入区 -->
            <section class="input-section">
                <h2 class="section-title">请告诉我们您遇到的问题</h2>
                <div class="input-grid">
                    <div class="input-group">
                        <label for="caseDescription">案情基本情况描述</label>
                        <textarea id="caseDescription" placeholder="请在这里详细描述事情的经过，涉及的人物、时间、地点等…"></textarea>
                    </div>
                    <div class="input-group">
                        <label for="legalClaim">您的主要诉讼请求</label>
                        <textarea id="legalClaim" placeholder="例如：要求对方赔偿损失10万元、要求对方履行合同等…"></textarea>
                    </div>
                </div>
                <div class="analyze-section">
                    <button id="analyzeBtn" class="analyze-btn">
                        <span>立即分析</span>
                    </button>
                </div>
            </section>

            <!-- 智能分析过程展示区 -->
            <section id="analysisSection" class="analysis-section" style="display: none;">
                <div class="analysis-background">
                    <canvas id="networkCanvas"></canvas>
                    <div class="analysis-content">
                        <div class="analysis-text">
                            <h3>AI正在深度分析您的案件...</h3>
                            <div class="progress-dots">
                                <span></span><span></span><span></span>
                            </div>
                        </div>
                        <button id="viewReportBtn" class="view-report-btn" style="display: none;">
                            查看证据链分析报告
                        </button>
                    </div>
                </div>
            </section>

            <!-- 分析成果展示区 -->
            <section id="resultsSection" class="results-section" style="display: none;">
                <!-- 证据链分析 -->
                <div id="evidenceChain" class="evidence-chain">
                    <h3>证据链分析图</h3>
                    <div class="evidence-timeline" id="evidenceTimeline">
                        <div class="evidence-container">
                            <!-- 左侧证据列表 -->
                            <div class="evidence-list" id="evidenceList">
                                <h4>证据详情</h4>
                                <!-- 证据详情列表将通过JavaScript动态生成 -->
                            </div>

                            <!-- 中间证据关系图 -->
                            <div class="evidence-graph" id="evidenceGraph">
                                <!-- 证据节点和连接线将通过JavaScript动态生成 -->
                            </div>

                            <!-- 右侧分析报告 -->
                            <div class="evidence-report" id="evidenceReport">
                                <h4>证据分析报告</h4>
                                <!-- 分析报告将通过JavaScript动态生成 -->
                            </div>
                        </div>
                    </div>
                    <button id="viewFullReportBtn" class="full-report-btn">
                        查看完整分析报告与文书
                    </button>
                </div>

                <!-- 综合报告与文书 -->
                <div id="fullReport" class="full-report" style="display: none;">
                    <div class="report-tabs">
                        <button class="tab-btn active" data-tab="legal-insights">法律洞察与案例指引</button>
                        <button class="tab-btn" data-tab="court-simulation">模拟法庭推演</button>
                        <button class="tab-btn" data-tab="legal-document">起诉文书初稿</button>
                    </div>

                    <div class="tab-content">
                        <div id="legal-insights" class="tab-panel active">
                            <!-- 法律洞察内容 -->
                        </div>
                        <div id="court-simulation" class="tab-panel">
                            <!-- 模拟法庭内容 -->
                        </div>
                        <div id="legal-document" class="tab-panel">
                            <!-- 法律文书内容 -->
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- 调试面板 - 已隐藏 -->
    <div id="debugPanel" class="debug-panel" style="display: none;">
        <div id="debugInfo">调试信息：<br></div>
        <button onclick="toggleDebug()">关闭</button>
    </div>

    <script>
        // 调试功能（已简化）
        function log(message) {
            // 只在控制台输出，不显示在页面上
            console.log(message);
        }

        function toggleDebug() {
            const panel = document.getElementById('debugPanel');
            panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
        }

        // 页面切换函数
        function switchToExperiencePage() {
            log('开始切换到体验页面');
            const welcomePage = document.getElementById('welcomePage');
            const experiencePage = document.getElementById('experiencePage');

            if (!welcomePage || !experiencePage) {
                log('错误：找不到页面元素');
                return;
            }

            welcomePage.classList.remove('active');
            experiencePage.classList.add('active');
            log('页面切换完成');

            // 1秒后自动填入示例数据
            setTimeout(() => {
                fillExampleData();
            }, 1000);
        }

        // 填充示例数据
        function fillExampleData() {
            log('开始填充示例数据');
            const caseDescription = document.getElementById('caseDescription');
            const legalClaim = document.getElementById('legalClaim');

            if (!caseDescription || !legalClaim) {
                log('错误：找不到输入框');
                return;
            }

            const sampleCase = "我真是气死了！今年7月15号，我在他们那个官方网站上花了一万两千多（具体是12888块）买了台叫'幻影X Pro'的笔记本，订单号是一串数字880123456789。18号电脑就到了，本来还挺开心的。结果好家伙，用了还不到一个月，从8月10号开始吧，这电脑就老是无缘无故蓝屏，然后卡死不动，机身烫得跟个暖手宝似的，我的好多工作文件都因为这个搞丢了！";
            const sampleClaim = "我就一个要求，把我买电脑的12888块钱全退给我！还有，因为这破电脑我好几天班都没上好，他们至少得赔我2000块钱的误工费！";

            // 先输入案情描述，完成后再输入诉讼请求
            typeWriter(caseDescription, sampleCase, 30, () => {
                // 案情描述输入完成后，等待500ms再开始输入诉讼请求
                setTimeout(() => {
                    typeWriter(legalClaim, sampleClaim, 30);
                }, 500);
            });
        }

        // 打字机效果
        function typeWriter(element, text, speed, callback) {
            element.value = '';
            let i = 0;

            function type() {
                if (i < text.length) {
                    element.value += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                } else if (callback) {
                    // 输入完成后执行回调函数
                    callback();
                }
            }

            type();
        }

        // 分析功能
        function startAnalysis() {
            log('开始分析');
            const caseDescription = document.getElementById('caseDescription').value;
            const legalClaim = document.getElementById('legalClaim').value;

            if (!caseDescription.trim() || !legalClaim.trim()) {
                alert('请填写案情描述和诉讼请求');
                return;
            }

            log('输入验证通过，开始显示分析过程');

            // 显示分析区域
            const analysisSection = document.getElementById('analysisSection');
            analysisSection.style.display = 'block';

            // 滚动到分析区域
            setTimeout(() => {
                analysisSection.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 开始网络动画
            startNetworkAnimation();

            // 3秒后显示查看报告按钮
            setTimeout(() => {
                const viewReportBtn = document.getElementById('viewReportBtn');
                viewReportBtn.style.display = 'block';
                viewReportBtn.style.animation = 'fadeIn 0.5s ease';
                log('查看报告按钮已显示');
            }, 3000);
        }

        // 网络动画
        function startNetworkAnimation() {
            const canvas = document.getElementById('networkCanvas');
            if (!canvas) {
                log('找不到画布元素');
                return;
            }

            const ctx = canvas.getContext('2d');
            canvas.width = canvas.parentElement.offsetWidth;
            canvas.height = canvas.parentElement.offsetHeight;

            // 节点和连接
            const nodes = [];
            const connections = [];
            const keywords = ['借条', '转账记录', '违约', '张三', '合同', '证据', '质检报告', '客服记录'];

            // 创建节点
            for (let i = 0; i < 8; i++) {
                nodes.push({
                    x: Math.random() * canvas.width,
                    y: Math.random() * canvas.height,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    radius: Math.random() * 5 + 3,
                    keyword: keywords[i],
                    alpha: Math.random()
                });
            }

            // 创建连接
            for (let i = 0; i < nodes.length; i++) {
                for (let j = i + 1; j < nodes.length; j++) {
                    if (Math.random() < 0.3) {
                        connections.push({ from: i, to: j, alpha: 0 });
                    }
                }
            }

            let animationId;
            function animate() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // 更新和绘制连接
                connections.forEach(conn => {
                    const fromNode = nodes[conn.from];
                    const toNode = nodes[conn.to];

                    conn.alpha = Math.min(conn.alpha + 0.01, 0.3);

                    ctx.strokeStyle = `rgba(179, 205, 224, ${conn.alpha})`;
                    ctx.lineWidth = 1;
                    ctx.beginPath();
                    ctx.moveTo(fromNode.x, fromNode.y);
                    ctx.lineTo(toNode.x, toNode.y);
                    ctx.stroke();
                });

                // 更新和绘制节点
                nodes.forEach(node => {
                    // 更新位置
                    node.x += node.vx;
                    node.y += node.vy;

                    // 边界反弹
                    if (node.x < 0 || node.x > canvas.width) node.vx *= -1;
                    if (node.y < 0 || node.y > canvas.height) node.vy *= -1;

                    // 更新透明度
                    node.alpha = Math.sin(Date.now() * 0.003 + node.x * 0.01) * 0.3 + 0.7;

                    // 绘制节点
                    ctx.fillStyle = `rgba(179, 205, 224, ${node.alpha})`;
                    ctx.beginPath();
                    ctx.arc(node.x, node.y, node.radius, 0, Math.PI * 2);
                    ctx.fill();

                    // 绘制关键词
                    ctx.fillStyle = `rgba(51, 51, 51, ${node.alpha * 0.8})`;
                    ctx.font = '12px Noto Sans SC';
                    ctx.textAlign = 'center';
                    ctx.fillText(node.keyword, node.x, node.y - node.radius - 5);
                });

                animationId = requestAnimationFrame(animate);
            }

            animate();
            log('网络动画已启动');
        }

        // 显示证据链
        function showEvidenceChain() {
            log('显示证据链分析');

            // 显示结果区域
            const resultsSection = document.getElementById('resultsSection');
            resultsSection.style.display = 'block';

            // 滚动到结果区域
            setTimeout(() => {
                resultsSection.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 生成证据链节点
            generateEvidenceNodes();
        }

        // 生成证据链图
        function generateEvidenceNodes() {
            const graph = document.getElementById('evidenceGraph');
            const list = document.getElementById('evidenceList');
            const report = document.getElementById('evidenceReport');

            graph.innerHTML = '';
            list.innerHTML = '<h4>证据详情</h4>';
            report.innerHTML = '<h4>证据分析报告</h4>';

            // 证据数据，包含位置信息（考虑容器内边距20px，节点宽度140px）
            // 有效绘图区域宽度约为容器宽度-40px（左右各20px内边距）
            const evidenceData = [
                { id: 'order', label: "网络购物订单", type: "evidence_owned", desc: "订单号880123456789，购买时间2025年7月15日", x: 10, y: 60 },
                { id: 'payment', label: "在线支付凭证", type: "evidence_owned", desc: "支付金额12,888元的电子凭证", x: 300, y: 60 },
                { id: 'contract', label: "建立买卖关系", type: "conclusion", desc: "通过订单和支付记录证明双方存在买卖合同关系", x: 155, y: 180 },
                { id: 'malfunction', label: "故障现象录像", type: "evidence_owned", desc: "记录设备故障的视频和照片证据", x: 480, y: 100 },
                { id: 'service', label: "客服沟通记录", type: "evidence_owned", desc: "完整的客服对话记录，证明商家推诿责任", x: 650, y: 100 },
                { id: 'defect', label: "产品质量缺陷", type: "conclusion", desc: "设备频繁蓝屏、死机、过热等故障现象", x: 565, y: 240 },
                { id: 'inspection', label: "第三方质检报告", type: "evidence_gap", desc: "⚠️ 缺失：权威机构出具的产品质量检测报告", x: 240, y: 360 },
                { id: 'claim', label: "退款退赔诉求", type: "final_claim", desc: "基于以上证据支持退款12,888元及误工费2,000元的诉求", x: 350, y: 480 }
            ];

            // 连接关系
            const connections = [
                { from: 'order', to: 'contract' },
                { from: 'payment', to: 'contract' },
                { from: 'malfunction', to: 'defect' },
                { from: 'service', to: 'defect' },
                { from: 'contract', to: 'claim' },
                { from: 'defect', to: 'claim' },
                { from: 'inspection', to: 'claim', type: 'gap' }
            ];

            // 创建SVG容器用于连接线和箭头
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.style.position = 'absolute';
            svg.style.top = '0';
            svg.style.left = '0';
            svg.style.width = '100%';
            svg.style.height = '100%';
            svg.style.pointerEvents = 'none';
            svg.style.zIndex = '1';

            // 定义箭头标记
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');

            const arrowMarker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarker.setAttribute('id', 'arrowhead');
            arrowMarker.setAttribute('markerWidth', '8');
            arrowMarker.setAttribute('markerHeight', '6');
            arrowMarker.setAttribute('refX', '7');
            arrowMarker.setAttribute('refY', '3');
            arrowMarker.setAttribute('orient', 'auto');

            const arrowPath = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPath.setAttribute('points', '0 0, 8 3, 0 6');
            arrowPath.setAttribute('fill', '#B3CDE0');
            arrowMarker.appendChild(arrowPath);
            defs.appendChild(arrowMarker);

            const arrowMarkerGap = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            arrowMarkerGap.setAttribute('id', 'arrowhead-gap');
            arrowMarkerGap.setAttribute('markerWidth', '8');
            arrowMarkerGap.setAttribute('markerHeight', '6');
            arrowMarkerGap.setAttribute('refX', '7');
            arrowMarkerGap.setAttribute('refY', '3');
            arrowMarkerGap.setAttribute('orient', 'auto');

            const arrowPathGap = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            arrowPathGap.setAttribute('points', '0 0, 8 3, 0 6');
            arrowPathGap.setAttribute('fill', '#ff6b6b');
            arrowMarkerGap.appendChild(arrowPathGap);
            defs.appendChild(arrowMarkerGap);

            svg.appendChild(defs);
            graph.appendChild(svg);

            // 计算连接线的起点和终点（指向节点边框而不是中心）
            function calculateConnectionPoints(fromNode, toNode) {
                const fromCenterX = fromNode.x + 70; // 节点宽度140px的一半
                const fromCenterY = fromNode.y + 45; // 节点高度90px的一半
                const toCenterX = toNode.x + 70;
                const toCenterY = toNode.y + 45;

                const dx = toCenterX - fromCenterX;
                const dy = toCenterY - fromCenterY;
                const distance = Math.sqrt(dx * dx + dy * dy);

                // 根据节点类型计算连接半径
                function getNodeRadius(node) {
                    if (node.type === 'evidence_gap') {
                        // 圆形节点，使用圆形半径
                        return Math.min(70, 45); // 取宽高的较小值作为半径
                    } else if (node.type === 'conclusion') {
                        // 菱形节点，使用对角线的一半
                        return Math.sqrt(70*70 + 45*45) / 2;
                    } else {
                        // 矩形节点，根据角度计算
                        const angle = Math.atan2(dy, dx);
                        const absAngle = Math.abs(angle);
                        if (absAngle < Math.atan2(90, 140)) {
                            return 70; // 连接到左右边
                        } else {
                            return 45; // 连接到上下边
                        }
                    }
                }

                const fromRadius = getNodeRadius(fromNode);
                const toRadius = getNodeRadius(toNode);

                const fromX = fromCenterX + (dx / distance) * fromRadius;
                const fromY = fromCenterY + (dy / distance) * fromRadius;
                const toX = toCenterX - (dx / distance) * toRadius;
                const toY = toCenterY - (dy / distance) * toRadius;

                return { fromX, fromY, toX, toY };
            }

            // 创建连接线
            connections.forEach((conn, index) => {
                const fromNode = evidenceData.find(n => n.id === conn.from);
                const toNode = evidenceData.find(n => n.id === conn.to);

                if (fromNode && toNode) {
                    const points = calculateConnectionPoints(fromNode, toNode);

                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', points.fromX);
                    line.setAttribute('y1', points.fromY);
                    line.setAttribute('x2', points.toX);
                    line.setAttribute('y2', points.toY);
                    line.style.stroke = conn.type === 'gap' ? '#ff6b6b' : '#B3CDE0';
                    line.style.strokeWidth = '3';
                    line.style.opacity = '0';
                    line.style.animation = `lineAppear 1s ease ${index * 0.3}s forwards`;
                    line.setAttribute('marker-end', conn.type === 'gap' ? 'url(#arrowhead-gap)' : 'url(#arrowhead)');

                    if (conn.type === 'gap') {
                        line.style.strokeDasharray = '8,4';
                    }

                    svg.appendChild(line);
                }
            });

            // 创建证据节点
            evidenceData.forEach((node, index) => {
                const nodeElement = document.createElement('div');
                nodeElement.className = `evidence-node ${node.type}`;
                nodeElement.style.left = `${node.x}px`;
                nodeElement.style.top = `${node.y}px`;
                nodeElement.style.animationDelay = `${index * 0.2}s`;
                nodeElement.style.zIndex = '2';

                nodeElement.innerHTML = `<h4>${node.label}</h4>`;
                graph.appendChild(nodeElement);

                // 创建左侧列表项
                const listItem = document.createElement('div');
                listItem.className = `evidence-list-item ${node.type}`;
                listItem.innerHTML = `
                    <h5>${node.label}</h5>
                    <p>${node.desc}</p>
                `;
                list.appendChild(listItem);
            });

            // 生成右侧分析报告
            generateEvidenceReport(report);

            log('证据链图生成完成');
        }

        // 生成证据分析报告
        function generateEvidenceReport(reportContainer) {
            const reportContent = `
                <div class="report-section">
                    <h5>证据完整性评估</h5>
                    <p>当前证据链完整度：<strong>75%</strong></p>
                    <div class="report-highlight">
                        <p><strong>优势证据：</strong>购买凭证完整，故障现象有记录，客服推诿有证据。</p>
                    </div>
                    <div class="report-highlight">
                        <p><strong>关键缺口：</strong>缺少第三方权威质检报告，可能影响胜诉概率。</p>
                    </div>
                </div>

                <div class="report-section">
                    <h5>证据链逻辑分析</h5>
                    <p>1. <strong>合同关系确立</strong>：订单+支付凭证 → 买卖关系成立</p>
                    <p>2. <strong>违约事实认定</strong>：故障录像+客服记录 → 产品质量缺陷</p>
                    <p>3. <strong>责任归属</strong>：质量缺陷+合同关系 → 商家违约责任</p>
                </div>

                <div class="report-section">
                    <h5>补强建议</h5>
                    <p>• 立即委托有资质的检测机构进行产品质量检测</p>
                    <p>• 收集更多同型号产品的质量投诉案例</p>
                    <p>• 整理因设备故障导致的具体损失证据</p>
                </div>

                <div class="report-section">
                    <h5>胜诉概率预测</h5>
                    <p>基于当前证据：<strong style="color: #F5A623;">70-80%</strong></p>
                    <p>补强证据后：<strong style="color: #A2D9CE;">85-95%</strong></p>
                </div>
            `;

            reportContainer.innerHTML += reportContent;
        }

        // 显示完整报告
        function showFullReport() {
            log('显示完整报告');
            const fullReport = document.getElementById('fullReport');
            fullReport.style.display = 'block';

            // 滚动到完整报告
            setTimeout(() => {
                fullReport.scrollIntoView({ behavior: 'smooth' });
            }, 100);

            // 加载报告内容
            loadReportContent();
        }

        // 加载报告内容
        function loadReportContent() {
            // 法律洞察内容
            const legalInsights = document.getElementById('legal-insights');
            legalInsights.innerHTML = `
                <div class="report-content">
                    <h4>相关法律条文</h4>
                    <div class="statute-item">
                        <h5>《中华人民共和国消费者权益保护法》第二十四条</h5>
                        <p>经营者提供的商品或者服务不符合质量要求的，消费者可以依照国家规定、当事人约定退货，或者要求经营者履行更换、修理等义务...</p>
                    </div>
                    <div class="statute-item">
                        <h5>《中华人民共和国民法典》第五百七十七条</h5>
                        <p>当事人一方不履行合同义务或者履行合同义务不符合约定的，应当承担继续履行、采取补救措施或者赔偿损失等违约责任。</p>
                    </div>

                    <h4>指导案例</h4>
                    <div class="case-item">
                        <h5>王某诉某电子品牌商产品责任纠纷案</h5>
                        <p><strong>案号：</strong>（2024）沪01民终XXXX号</p>
                        <p>法院认为，消费者购买的产品在"三包"期内出现非人为性能故障，经两次修理仍不能正常使用的，经营者应当负责更换或者退货。本案中，消费者提交的证据足以形成优势证据，证明产品存在质量问题，故支持其退货退款的诉讼请求。</p>
                    </div>
                </div>
            `;

            // 模拟法庭内容
            const courtSimulation = document.getElementById('court-simulation');
            courtSimulation.innerHTML = `
                <div class="report-content">
                    <h4>我方论点</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="background: rgba(179, 205, 224, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #B3CDE0;">产品在三包期内出现严重性能故障，符合法定退货条件。</li>
                        <li style="background: rgba(179, 205, 224, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #B3CDE0;">被告以不实理由（如软件不兼容）推卸责任，属于恶意违约。</li>
                        <li style="background: rgba(179, 205, 224, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #B3CDE0;">产品质量问题直接导致了原告的误工损失，应予赔偿。</li>
                    </ul>

                    <h4>对方可能的抗辩</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="background: rgba(255, 107, 107, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #ff6b6b;">故障可能是由原告自行安装的第三方软件或病毒导致，非产品本身质量问题。</li>
                        <li style="background: rgba(255, 107, 107, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #ff6b6b;">原告未能提供权威的检测报告证明产品存在固有缺陷。</li>
                        <li style="background: rgba(255, 107, 107, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #ff6b6b;">原告主张的误工损失缺乏充分的证据支持。</li>
                    </ul>

                    <h4>策略建议</h4>
                    <ul style="list-style: none; padding: 0;">
                        <li style="background: rgba(162, 217, 206, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #A2D9CE;">首要策略：立即将设备送至具有司法鉴定资质的第三方检测机构进行检测，获取产品存在质量缺陷的直接证据。</li>
                        <li style="background: rgba(162, 217, 206, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #A2D9CE;">次要策略：整理并公证与客服的完整沟通记录，固定被告消极应对和推诿责任的证据。</li>
                        <li style="background: rgba(162, 217, 206, 0.1); padding: 1rem; margin: 0.5rem 0; border-radius: 8px; border-left: 3px solid #A2D9CE;">补充策略：准备误工损失的相关证据，如劳动合同、请假记录或因设备问题导致项目延误的沟通邮件等。</li>
                    </ul>
                </div>
            `;

            // 法律文书内容
            const legalDocument = document.getElementById('legal-document');
            legalDocument.innerHTML = `
                <div class="document-content">
                    <div class="document-header">
                        <h3>民事起诉状</h3>
                    </div>
                    <div class="document-body">
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>原告：</strong>张三，男，1995年10月20日生，汉族</p>
                            <p>身份证号：11010119951020XXXX</p>
                            <p>住址：北京市朝阳区建国路XX号院X号楼X单元XXX室</p>
                            <p>联系电话：138-0013-8000</p>
                        </div>
                        <div style="background: rgba(245, 245, 245, 0.5); border-radius: 8px; padding: 1rem; margin: 1rem 0; line-height: 1.8;">
                            <p><strong>被告：</strong>未来科技电脑有限公司</p>
                            <p>统一社会信用代码：91440300MA5GXXXXXX</p>
                            <p>住所地：广东省深圳市南山区科技园路XX号</p>
                            <p>法定代表人：李四</p>
                            <p>联系电话：0755-88886666</p>
                        </div>
                        <div style="text-align: center; font-size: 1.1rem; font-weight: 500; margin: 2rem 0; padding: 1rem; background: rgba(179, 205, 224, 0.1); border-radius: 8px;">
                            <p><strong>案由：</strong>买卖合同纠纷</p>
                        </div>
                        <div style="margin: 2rem 0;">
                            <h4 style="color: #333; font-size: 1.2rem; font-weight: 500; margin-bottom: 1rem;">诉讼请求：</h4>
                            <ol style="padding-left: 2rem;">
                                <li style="margin: 0.5rem 0; line-height: 1.8;">一、判令被告立即退还原告购物款人民币12,888元；</li>
                                <li style="margin: 0.5rem 0; line-height: 1.8;">二、判令被告赔偿原告因处理本事件导致的误工损失人民币2,000元；</li>
                                <li style="margin: 0.5rem 0; line-height: 1.8;">三、判令被告承担本案全部诉讼费用。</li>
                            </ol>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 2px solid #f0f0f0;">
                        <button class="download-btn" onclick="downloadDocument()">下载完整文书</button>
                    </div>
                </div>
            `;

            log('报告内容加载完成');
        }

        // 标签页切换
        function switchTab(tabId) {
            log('切换标签页: ' + tabId);
            // 移除所有活动状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

            // 添加活动状态
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
            document.getElementById(tabId).classList.add('active');
        }

        // 下载文档（模拟功能）
        function downloadDocument() {
            alert('文档下载功能已触发！在实际应用中，这里会生成并下载PDF文件。');
            log('文档下载功能被调用');
        }

        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM加载完成，开始初始化');

            // 绑定事件
            const startBtn = document.getElementById('startExperienceBtn');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const viewReportBtn = document.getElementById('viewReportBtn');
            const viewFullReportBtn = document.getElementById('viewFullReportBtn');

            if (startBtn) {
                startBtn.addEventListener('click', switchToExperiencePage);
                log('立即体验按钮事件绑定成功');
            } else {
                log('错误：找不到立即体验按钮');
            }

            if (analyzeBtn) {
                analyzeBtn.addEventListener('click', startAnalysis);
                log('立即分析按钮事件绑定成功');
            }

            if (viewReportBtn) {
                viewReportBtn.addEventListener('click', showEvidenceChain);
                log('查看报告按钮事件绑定成功');
            }

            if (viewFullReportBtn) {
                viewFullReportBtn.addEventListener('click', showFullReport);
                log('查看完整报告按钮事件绑定成功');
            }

            // 标签页切换事件
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    switchTab(btn.dataset.tab);
                });
            });
            log('标签页切换事件绑定成功');

            log('初始化完成');
        });

        log('脚本加载完成');
    </script>
</body>
</html>
</body>
</html>
